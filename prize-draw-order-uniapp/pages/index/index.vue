<template>
  <view class="container" :style="{ backgroundImage: backgroundImageStyle }"
    :class="{ 'has-bg-image': !!backgroundImageStyle }">
    <!-- 商家信息展示 -->
    <view class="merchant-info" v-if="merchantInfo">
      <!-- Logo图片 -->
      <view class="logo-container" v-if="logoImageUrl">
        <image :src="logoImageUrl" class="logo-img" mode="aspectFit"></image>
      </view>
      <!-- 欢迎语 -->
      <view class="welcome-text" v-if="welcomeText">{{ welcomeText }}</view>
      <view class="merchant-name">{{ merchantInfo.merchantName }}</view>
      <view class="merchant-address" v-if="merchantInfo.address">{{ merchantInfo.address }}</view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="function-buttons">
      <view class="button-row">
        <view class="function-btn order-btn" @click="goToOrder">
          <view class="btn-icon">🍽️</view>
          <view class="btn-text">点餐</view>
        </view>
        <view class="function-btn lottery-btn" @click="goToLottery">
          <view class="btn-icon">🎁</view>
          <view class="btn-text">抽奖</view>
        </view>
      </view>
    </view>

    <!-- 桌台信息 -->
    <view class="table-info" v-if="tableInfo">
      <view class="table-number">桌台：{{ tableInfo.tableName || tableInfo.tableNumber }}</view>
    </view>

    <!-- 活动微信二维码 -->
    <view class="wechat-qrcode" v-if="currentActivity && currentActivity.wechatQrcode">
      <view class="qrcode-title">关注我们</view>
      <image :src="currentActivity.wechatQrcode" class="qrcode-img" mode="aspectFit"></image>
    </view>

    <!-- 到期提示弹窗 -->
    <u-modal v-model="expiredModal.show" title="系统提示" :show-cancel-button="false" confirm-text="我知道了"
      @confirm="expiredModal.show = false">
      <view class="expired-content">
        <view class="expired-icon">⚠️</view>
        <view class="expired-text">{{ expiredModal.message }}</view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { merchantApi, tableApi, configApi, lotteryApi, getImageUrl } from '@/utils/api.js'

export default {
  data() {
    return {
      merchantCode: '',
      tableNumber: '',
      merchantInfo: null,
      tableInfo: null,
      merchantConfig: {},
      currentActivity: null,
      expiredModal: {
        show: false,
        message: ''
      }
    }
  },

  computed: {
    // 解析UI配置
    uiConfig() {
      const uiConfigStr = this.merchantConfig.ui_config
      if (uiConfigStr) {
        try {
          return JSON.parse(uiConfigStr)
        } catch (e) {
          console.error('UI配置解析失败:', e)
          return {}
        }
      }
      return {}
    },

    // 背景图片样式
    backgroundImageStyle() {
      const backgroundImage = this.uiConfig.backgroundImage || this.merchantConfig.scan_page_bg
      if (backgroundImage) {
        const imageUrl = getImageUrl(backgroundImage)
        return `url('${imageUrl}')`
      }
      return ''
    },

    // Logo图片URL
    logoImageUrl() {
      const logoImage = this.uiConfig.logoImage
      return logoImage ? getImageUrl(logoImage) : ''
    },

    // 欢迎语
    welcomeText() {
      return this.uiConfig.welcomeText || ''
    },

    // 页面标题
    pageTitle() {
      return this.uiConfig.pageTitle || '抽奖点餐'
    }
  },

  onLoad(options) {
    // 从URL参数或扫码获取商家编码和桌台号
    this.merchantCode = options.merchantCode || '002'
    this.tableNumber = options.tableNumber || 'A002'

    this.initPage()
  },

  watch: {
    // 监听页面标题变化，动态设置导航栏标题
    pageTitle: {
      handler(newTitle) {
        if (newTitle) {
          uni.setNavigationBarTitle({
            title: newTitle
          })
        }
      },
      immediate: true
    }
  },

  methods: {
    async initPage() {
      try {
        // 加载商家信息
        await this.loadMerchantInfo()

        // 加载桌台信息
        if (this.tableNumber) {
          await this.loadTableInfo()
        }

        // 加载商家配置
        await this.loadMerchantConfig()

        // 加载当前活动信息
        await this.loadCurrentActivity()

      } catch (error) {
        console.error('页面初始化失败:', error)
        this.handleError(error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await merchantApi.getMerchantInfo(this.merchantCode)
        if (res.code === 200) {
          this.merchantInfo = res.data
        } else {
          throw new Error(res.msg || '获取商家信息失败')
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadTableInfo() {
      try {
        const res = await tableApi.getTableInfo(this.merchantCode, this.tableNumber)
        if (res.code === 200) {
          this.tableInfo = res.data
        } else {
          throw new Error(res.msg || '获取桌台信息失败')
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadMerchantConfig() {
      try {
        const res = await configApi.getAllConfig(this.merchantCode)
        if (res.code === 200) {
          this.merchantConfig = res.data || {}
        }
      } catch (error) {
        console.error('获取商家配置失败:', error)
      }
    },

    async loadCurrentActivity() {
      try {
        const res = await lotteryApi.getCurrentActivity(this.merchantCode)
        if (res.code === 200 && res.data) {
          this.currentActivity = res.data
        }
      } catch (error) {
        console.error('获取活动信息失败:', error)
      }
    },

    goToOrder() {
      if (!this.tableInfo || !this.tableInfo.meituanLink) {
        uni.showToast({
          title: '点餐功能暂未开放',
          icon: 'none'
        })
        return
      }

      // 跳转到美团点餐链接
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(this.tableInfo.meituanLink)}`
      })
    },

    goToLottery() {
      // 跳转到抽奖页面
      uni.navigateTo({
        url: `/pages/lottery/lottery?merchantCode=${this.merchantCode}&tableNumber=${this.tableNumber}`
      })
    },

    handleError(error) {
      let message = error.message || error.msg || '系统异常'

      // 检查是否是商家到期错误
      if (message.includes('过期') || message.includes('到期')) {
        this.expiredModal.message = message
        this.expiredModal.show = true
      } else {
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 40rpx 30rpx;
  position: relative;

  // 确保内容在遮罩之上
  >* {
    position: relative;
    z-index: 2;
  }

  // 只有当有背景图片时才添加遮罩
  &.has-bg-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
  }
}

.merchant-info {
  text-align: center;
  margin-bottom: 80rpx;

  .logo-container {
    margin-bottom: 30rpx;

    .logo-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      background: rgba(255, 255, 255, 0.9);
      padding: 10rpx;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
    }
  }

  .welcome-text {
    font-size: 32rpx;
    color: #fff;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  .merchant-name {
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  .merchant-address {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }
}

.function-buttons {
  margin-bottom: 60rpx;

  .button-row {
    display: flex;
    justify-content: space-around;
    gap: 40rpx;
  }

  .function-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }

    .btn-icon {
      font-size: 80rpx;
      margin-bottom: 20rpx;
    }

    .btn-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .order-btn {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }

  .lottery-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
}

.table-info {
  text-align: center;
  margin-bottom: 60rpx;

  .table-number {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    padding: 20rpx 40rpx;
    border-radius: 50rpx;
    display: inline-block;
  }
}

.wechat-qrcode {
  text-align: center;

  .qrcode-title {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30rpx;
  }

  .qrcode-img {
    width: 300rpx;
    height: 300rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
  }
}

.expired-content {
  text-align: center;
  padding: 40rpx 20rpx;

  .expired-icon {
    font-size: 80rpx;
    margin-bottom: 30rpx;
  }

  .expired-text {
    font-size: 32rpx;
    color: #666;
    line-height: 1.6;
  }
}
</style>