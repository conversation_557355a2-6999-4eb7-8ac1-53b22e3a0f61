<template>
  <view class="result-container">
    <!-- 结果展示 -->
    <view class="result-content">
      <view class="result-icon">
        <text v-if="isWinner">🎉</text>
        <text v-else>😊</text>
      </view>

      <view class="result-title">
        <text v-if="isWinner">恭喜中奖！</text>
        <text v-else>谢谢参与</text>
      </view>

      <view class="result-desc">
        <text v-if="isWinner">您获得了：{{ prizeName }}</text>
        <text v-else>很遗憾，这次没有中奖，再接再厉！</text>
      </view>

      <!-- 中奖信息 -->
      <view class="prize-info" v-if="isWinner && recordDetail">
        <view class="info-item">
          <text class="label">奖品名称：</text>
          <text class="value">{{ recordDetail.prizeName }}</text>
        </view>
        <view class="info-item" v-if="recordDetail.prizeDesc">
          <text class="label">奖品描述：</text>
          <text class="value">{{ recordDetail.prizeDesc }}</text>
        </view>
        <view class="info-item">
          <text class="label">中奖时间：</text>
          <text class="value">{{ formatTime(recordDetail.drawTime) }}</text>
        </view>
        <view class="info-item">
          <text class="label">领取状态：</text>
          <text class="value status" :class="{ 'claimed': recordDetail.claimStatus === '1' }">
            {{ recordDetail.claimStatus === '1' ? '已领取' : '待领取' }}
          </text>
        </view>
      </view>

      <!-- 领取说明 -->
      <view class="claim-instruction" v-if="isWinner && claimInstruction">
        <view class="instruction-title">领取说明</view>
        <view class="instruction-content">{{ claimInstruction }}</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="button-row">
        <view class="action-btn secondary" @click="goBack">
          <text>返回抽奖</text>
        </view>
        <view class="action-btn primary" @click="handleClaim"
          v-if="isWinner && recordDetail && recordDetail.claimStatus === '0'">
          <text>立即领取</text>
        </view>
        <view class="action-btn primary" @click="viewRecords" v-else>
          <text>查看记录</text>
        </view>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view class="share-section" v-if="isWinner">
      <view class="share-btn" @click="shareResult">
        <text>分享好运</text>
      </view>
    </view>
  </view>
</template>

<script>
import { lotteryApi, configApi } from '@/utils/api.js'

export default {
  data() {
    return {
      recordId: '',
      recordDetail: null,
      claimInstruction: '',
      isWinner: false,
      prizeName: ''
    }
  },

  onLoad(options) {
    this.recordId = options.recordId || ''
    this.isWinner = options.isWinner === '1' || options.isWinner === 'true'
    this.prizeName = options.prizeName || ''

    if (this.recordId) {
      this.loadRecordDetail()
    }
  },

  methods: {
    async loadRecordDetail() {
      try {
        const res = await lotteryApi.getRecordDetail(this.recordId)
        if (res.code === 200) {
          this.recordDetail = res.data
          this.isWinner = this.recordDetail.isWinner === '1'
          this.prizeName = this.recordDetail.prizeName

          // 如果中奖，加载领取说明
          if (this.isWinner) {
            await this.loadClaimInstruction()
          }
        }
      } catch (error) {
        console.error('获取记录详情失败:', error)
        uni.showToast({
          title: '获取详情失败',
          icon: 'none'
        })
      }
    },

    async loadClaimInstruction() {
      if (!this.recordDetail || !this.recordDetail.activityId) return

      try {
        // 从活动信息中获取领取说明
        const res = await lotteryApi.getLotteryActivity(this.recordDetail.activityId)
        if (res.code === 200 && res.data) {
          this.claimInstruction = res.data.claimInstruction || '请到前台出示此页面领取奖品'
        }
      } catch (error) {
        console.error('获取领取说明失败:', error)
        this.claimInstruction = '请到前台出示此页面领取奖品'
      }
    },

    handleClaim() {
      if (!this.recordDetail) return

      // 跳转到领取页面
      uni.navigateTo({
        url: `/pages/claim/claim?recordId=${this.recordId}`
      })
    },

    goBack() {
      // 返回上一页或抽奖页面
      const pages = getCurrentPages()
      if (pages.length > 1) {
        uni.navigateBack()
      } else {
        uni.redirectTo({
          url: '/pages/lottery/lottery'
        })
      }
    },

    viewRecords() {
      // 跳转到记录页面
      const userOpenid = this.recordDetail ? this.recordDetail.userOpenid : ''
      uni.navigateTo({
        url: `/pages/records/records?userOpenid=${userOpenid}`
      })
    },

    shareResult() {
      if (!this.isWinner) return

      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          console.log('分享菜单显示成功')
        },
        fail: (err) => {
          console.error('分享菜单显示失败:', err)
          uni.showToast({
            title: '分享功能暂不可用',
            icon: 'none'
          })
        }
      })
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
  },

  // 分享配置
  onShareAppMessage() {
    if (this.isWinner) {
      return {
        title: `我在抽奖中获得了${this.prizeName}！`,
        path: '/pages/index/index',
        imageUrl: '' // 可以设置分享图片
      }
    }
    return {
      title: '快来参与抽奖活动！',
      path: '/pages/index/index'
    }
  }
}
</script>

<style lang="scss" scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.result-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 60rpx;

  .result-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
  }

  .result-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .result-desc {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }
}

.prize-info {
  text-align: left;
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;

  .info-item {
    display: flex;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }

    .value {
      font-size: 28rpx;
      color: #333;
      flex: 1;

      &.status {
        color: #ff6b6b;

        &.claimed {
          color: #51cf66;
        }
      }
    }
  }
}

.claim-instruction {
  text-align: left;
  background: #fff3cd;
  border-radius: 20rpx;
  padding: 30rpx;

  .instruction-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #856404;
    margin-bottom: 15rpx;
  }

  .instruction-content {
    font-size: 28rpx;
    color: #856404;
    line-height: 1.6;
  }
}

.action-buttons {
  margin-bottom: 40rpx;

  .button-row {
    display: flex;
    gap: 30rpx;
  }

  .action-btn {
    flex: 1;
    padding: 30rpx 0;
    border-radius: 50rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;

    &.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
    }

    &.secondary {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      border: 2rpx solid rgba(255, 255, 255, 0.5);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.share-section {
  text-align: center;

  .share-btn {
    display: inline-block;
    padding: 20rpx 60rpx;
    background: rgba(255, 255, 255, 0.2);
    border: 2rpx solid rgba(255, 255, 255, 0.5);
    border-radius: 50rpx;
    color: #fff;
    font-size: 28rpx;

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>